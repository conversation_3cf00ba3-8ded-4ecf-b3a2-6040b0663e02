"use strict";
/**
 * 环境感知组件
 *
 * 该组件使实体能够感知周围的环境，包括环境类型、天气、光照条件、地形等。
 * 可以附加到角色或其他需要环境感知能力的实体上。
 */
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EnvironmentAwarenessComponent = exports.TerrainType = exports.WeatherType = exports.EnvironmentType = void 0;
var Component_1 = require("../../../core/Component");
var three_1 = require("three");
/**
 * 环境类型枚举
 */
var EnvironmentType;
(function (EnvironmentType) {
    EnvironmentType["INDOOR"] = "indoor";
    EnvironmentType["OUTDOOR"] = "outdoor";
    EnvironmentType["UNDERWATER"] = "underwater";
    EnvironmentType["SPACE"] = "space";
    EnvironmentType["CAVE"] = "cave";
    EnvironmentType["FOREST"] = "forest";
    EnvironmentType["DESERT"] = "desert";
    EnvironmentType["SNOW"] = "snow";
    EnvironmentType["URBAN"] = "urban";
    EnvironmentType["CUSTOM"] = "custom";
})(EnvironmentType || (exports.EnvironmentType = EnvironmentType = {}));
/**
 * 天气类型枚举
 */
var WeatherType;
(function (WeatherType) {
    WeatherType["CLEAR"] = "clear";
    WeatherType["CLOUDY"] = "cloudy";
    WeatherType["RAINY"] = "rainy";
    WeatherType["STORMY"] = "stormy";
    WeatherType["SNOWY"] = "snowy";
    WeatherType["FOGGY"] = "foggy";
    WeatherType["CUSTOM"] = "custom";
})(WeatherType || (exports.WeatherType = WeatherType = {}));
/**
 * 地形类型枚举
 */
var TerrainType;
(function (TerrainType) {
    TerrainType["FLAT"] = "flat";
    TerrainType["HILLS"] = "hills";
    TerrainType["MOUNTAINS"] = "mountains";
    TerrainType["WATER"] = "water";
    TerrainType["URBAN"] = "urban";
    TerrainType["CUSTOM"] = "custom";
})(TerrainType || (exports.TerrainType = TerrainType = {}));
/**
 * 环境感知组件
 */
var EnvironmentAwarenessComponent = /** @class */ (function (_super) {
    __extends(EnvironmentAwarenessComponent, _super);
    /**
     * 构造函数
     * @param entity 实体
     * @param config 配置
     */
    function EnvironmentAwarenessComponent(entity, config) {
        if (config === void 0) { config = {}; }
        var _this = _super.call(this, entity) || this;
        // 上次更新时间
        _this.lastUpdateTime = 0;
        // 环境变化回调函数
        _this.onEnvironmentChangeCallbacks = [];
        // 设置默认配置
        _this.config = __assign({ awarenessRange: 50, updateFrequency: 1000, debug: false, autoDetect: true, changeThreshold: 0.1 }, config);
        // 初始化环境感知数据
        _this.data = {
            environmentType: EnvironmentType.OUTDOOR,
            weatherType: WeatherType.CLEAR,
            terrainType: TerrainType.FLAT,
            lightIntensity: 1.0,
            temperature: 20,
            humidity: 0.5,
            windSpeed: 0,
            windDirection: new three_1.Vector3(1, 0, 0),
            noiseLevel: 0,
            airQuality: 1.0,
            waterLevel: 0,
            visibility: 1000,
            timeOfDay: 12,
            customParameters: new Map(),
            lastEnvironmentChangeTime: Date.now(),
            awarenessRange: _this.config.awarenessRange || 50
        };
        return _this;
    }
    /**
     * 更新环境感知数据
     * @param data 新的环境感知数据
     */
    EnvironmentAwarenessComponent.prototype.updateEnvironmentData = function (data) {
        var now = Date.now();
        var hasSignificantChange = this.hasSignificantChange(data);
        // 更新数据
        Object.assign(this.data, data);
        this.lastUpdateTime = now;
        // 如果有显著变化，触发环境变化事件
        if (hasSignificantChange) {
            this.data.lastEnvironmentChangeTime = now;
            this.notifyEnvironmentChange();
        }
        if (this.config.debug) {
            console.log('环境感知数据已更新:', this.data);
        }
    };
    /**
     * 检查是否有显著变化
     * @param newData 新数据
     * @returns 是否有显著变化
     */
    EnvironmentAwarenessComponent.prototype.hasSignificantChange = function (newData) {
        var threshold = this.config.changeThreshold || 0.1;
        // 检查枚举类型变化
        if ((newData.environmentType !== undefined && newData.environmentType !== this.data.environmentType) ||
            (newData.weatherType !== undefined && newData.weatherType !== this.data.weatherType) ||
            (newData.terrainType !== undefined && newData.terrainType !== this.data.terrainType)) {
            return true;
        }
        // 检查数值型变化
        if ((newData.lightIntensity !== undefined && Math.abs(newData.lightIntensity - this.data.lightIntensity) > threshold) ||
            (newData.temperature !== undefined && Math.abs(newData.temperature - this.data.temperature) > 2) ||
            (newData.humidity !== undefined && Math.abs(newData.humidity - this.data.humidity) > threshold) ||
            (newData.windSpeed !== undefined && Math.abs(newData.windSpeed - this.data.windSpeed) > 1) ||
            (newData.noiseLevel !== undefined && Math.abs(newData.noiseLevel - this.data.noiseLevel) > threshold) ||
            (newData.airQuality !== undefined && Math.abs(newData.airQuality - this.data.airQuality) > threshold) ||
            (newData.waterLevel !== undefined && Math.abs(newData.waterLevel - this.data.waterLevel) > 0.5) ||
            (newData.visibility !== undefined && Math.abs(newData.visibility - this.data.visibility) > 100) ||
            (newData.timeOfDay !== undefined && Math.abs(newData.timeOfDay - this.data.timeOfDay) > 1)) {
            return true;
        }
        return false;
    };
    /**
     * 注册环境变化回调
     * @param callback 回调函数
     */
    EnvironmentAwarenessComponent.prototype.onEnvironmentChange = function (callback) {
        this.onEnvironmentChangeCallbacks.push(callback);
    };
    /**
     * 移除环境变化回调
     * @param callback 回调函数
     */
    EnvironmentAwarenessComponent.prototype.removeEnvironmentChangeCallback = function (callback) {
        var index = this.onEnvironmentChangeCallbacks.indexOf(callback);
        if (index !== -1) {
            this.onEnvironmentChangeCallbacks.splice(index, 1);
        }
    };
    /**
     * 通知环境变化
     */
    EnvironmentAwarenessComponent.prototype.notifyEnvironmentChange = function () {
        for (var _i = 0, _a = this.onEnvironmentChangeCallbacks; _i < _a.length; _i++) {
            var callback = _a[_i];
            callback(this.data);
        }
    };
    /**
     * 获取当前环境数据
     * @returns 环境数据
     */
    EnvironmentAwarenessComponent.prototype.getEnvironmentData = function () {
        return __assign({}, this.data);
    };
    /**
     * 设置自定义环境参数
     * @param key 参数键
     * @param value 参数值
     */
    EnvironmentAwarenessComponent.prototype.setCustomParameter = function (key, value) {
        this.data.customParameters.set(key, value);
    };
    /**
     * 获取自定义环境参数
     * @param key 参数键
     * @returns 参数值
     */
    EnvironmentAwarenessComponent.prototype.getCustomParameter = function (key) {
        return this.data.customParameters.get(key);
    };
    return EnvironmentAwarenessComponent;
}(Component_1.Component));
exports.EnvironmentAwarenessComponent = EnvironmentAwarenessComponent;
