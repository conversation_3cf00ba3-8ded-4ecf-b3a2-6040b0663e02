/**
 * 环境感知系统
 *
 * 该系统负责检测和更新场景中的环境信息，并将其传递给具有环境感知组件的实体。
 * 系统会分析场景中的光照、天气、地形等元素，计算环境参数，并触发相应的环境变化事件。
 */

import { System } from '../../core/System';
import type { World } from '../../core/World';
import type { Entity } from '../../core/Entity';
import { Scene } from '../../scene/Scene';
import { Vector3, Raycaster } from 'three';
import { EnvironmentType, WeatherType, TerrainType, EnvironmentAwarenessData } from '../components/EnvironmentAwarenessComponent';

/**
 * 环境感知系统配置接口
 */
export interface EnvironmentAwarenessSystemConfig {
  // 是否启用调试模式
  debug?: boolean;
  // 更新频率 (毫秒)
  updateFrequency?: number;
  // 是否自动检测环境
  autoDetect?: boolean;
  // 环境检测范围 (米)
  detectionRange?: number;
  // 是否启用环境响应
  enableResponse?: boolean;
  // 是否使用物理射线检测
  usePhysicsRaycasting?: boolean;
  // 是否启用环境可视化
  enableVisualization?: boolean;
}

/**
 * 环境感知系统
 */
export class EnvironmentAwarenessSystem extends System {
  // 配置
  private config: EnvironmentAwarenessSystemConfig;
  // 上次更新时间
  private lastUpdateTime: number = 0;
  // 射线检测器
  private raycaster: Raycaster;
  // 环境感知组件实体列表
  private awarenessEntities: Entity[] = [];
  // 环境响应组件实体列表
  private responseEntities: Entity[] = [];
  // 环境区域映射
  private environmentZones: Map<string, EnvironmentZone> = new Map();
  // 全局环境数据
  private globalEnvironmentData: EnvironmentAwarenessData;

  /**
   * 构造函数
   * @param world 世界
   * @param config 配置
   */
  constructor(world: World, config: EnvironmentAwarenessSystemConfig = {}) {
    super();
    this.world = world;

    // 设置默认配置
    this.config = {
      debug: false,
      updateFrequency: 1000,
      autoDetect: true,
      detectionRange: 100,
      enableResponse: true,
      usePhysicsRaycasting: false,
      enableVisualization: false,
      ...config
    };

    // 初始化射线检测器
    this.raycaster = new Raycaster();

    // 初始化全局环境数据
    this.globalEnvironmentData = this.createDefaultEnvironmentData();

    if (this.config.debug) {
      console.log('环境感知系统初始化');
    }
  }

  /**
   * 创建默认环境数据
   * @returns 默认环境数据
   */
  private createDefaultEnvironmentData(): EnvironmentAwarenessData {
    return {
      environmentType: EnvironmentType.OUTDOOR,
      weatherType: WeatherType.CLEAR,
      terrainType: TerrainType.FLAT,
      lightIntensity: 1.0,
      temperature: 20,
      humidity: 0.5,
      windSpeed: 0,
      windDirection: new Vector3(1, 0, 0),
      noiseLevel: 0,
      airQuality: 1.0,
      waterLevel: 0,
      visibility: 1000,
      timeOfDay: 12,
      customParameters: new Map<string, any>(),
      lastEnvironmentChangeTime: Date.now(),
      awarenessRange: this.config.detectionRange || 100
    };
  }

  /**
   * 初始化
   */
  public initialize(): void {
    // 查找所有具有环境感知组件的实体
    this.awarenessEntities = this.findEntitiesWithComponent('EnvironmentAwarenessComponent');

    // 查找所有具有环境响应组件的实体
    this.responseEntities = this.findEntitiesWithComponent('EnvironmentResponseComponent');

    if (this.config.debug) {
      console.log(`找到 ${this.awarenessEntities.length} 个环境感知实体和 ${this.responseEntities.length} 个环境响应实体`);
    }
  }

  /**
   * 查找具有指定组件的实体
   * @param componentType 组件类型
   * @returns 实体数组
   */
  private findEntitiesWithComponent(componentType: string): Entity[] {
    const result: Entity[] = [];
    if (!this.world) return result;

    const allEntities = this.world.getAllEntities();
    for (const entity of allEntities) {
      if (entity.hasComponent(componentType)) {
        result.push(entity);
      }
    }
    return result;
  }

  /**
   * 更新
   * @param deltaTime 时间增量
   */
  public update(deltaTime: number): void {
    const now = Date.now();

    // 检查是否需要更新
    if (now - this.lastUpdateTime < this.config.updateFrequency!) {
      return;
    }

    this.lastUpdateTime = now;

    // 更新实体列表
    this.awarenessEntities = this.findEntitiesWithComponent('EnvironmentAwarenessComponent');
    this.responseEntities = this.findEntitiesWithComponent('EnvironmentResponseComponent');

    // 如果启用自动检测，更新全局环境数据
    if (this.config.autoDetect) {
      this.updateGlobalEnvironmentData();
    }

    // 更新每个实体的环境感知数据
    for (const entity of this.awarenessEntities) {
      this.updateEntityEnvironmentData(entity);
    }

    // 如果启用环境响应，处理环境响应
    if (this.config.enableResponse) {
      this.processEnvironmentResponses();
    }

    // 如果启用环境可视化，更新可视化
    if (this.config.enableVisualization) {
      this.updateVisualization();
    }
  }

  /**
   * 更新全局环境数据
   */
  private updateGlobalEnvironmentData(): void {
    // 获取当前场景
    const scene = this.world.getActiveScene();
    if (!scene) {
      return;
    }

    // 分析场景环境
    const environmentData = this.analyzeSceneEnvironment(scene);

    // 更新全局环境数据
    this.globalEnvironmentData = {
      ...this.globalEnvironmentData,
      ...environmentData
    };

    if (this.config.debug) {
      console.log('全局环境数据已更新:', this.globalEnvironmentData);
    }
  }

  /**
   * 分析场景环境
   * @param scene 场景
   * @returns 环境数据
   */
  private analyzeSceneEnvironment(scene: Scene): Partial<EnvironmentAwarenessData> {
    const data: Partial<EnvironmentAwarenessData> = {};

    // 分析光照
    data.lightIntensity = this.analyzeLighting(scene);

    // 分析天气
    data.weatherType = this.analyzeWeather(scene);

    // 分析时间
    data.timeOfDay = this.analyzeTimeOfDay(scene);

    // 分析环境类型
    data.environmentType = this.analyzeEnvironmentType(scene);

    // 分析地形
    data.terrainType = this.analyzeTerrainType(scene);

    // 分析其他环境参数
    data.temperature = this.calculateTemperature(data.environmentType!, data.timeOfDay, data.weatherType!);
    data.humidity = this.calculateHumidity(data.weatherType!, data.environmentType!);
    data.visibility = this.calculateVisibility(data.weatherType!, data.lightIntensity!);

    return data;
  }

  /**
   * 分析光照
   * @param scene 场景
   * @returns 光照强度
   */
  private analyzeLighting(scene: Scene): number {
    // 简化的光照分析，使用默认值
    // 由于Scene类没有getLights方法，我们使用默认的中等光照
    return 0.5;
  }

  /**
   * 分析天气
   * @param scene 场景
   * @returns 天气类型
   */
  private analyzeWeather(scene: Scene): WeatherType {
    // 简化的天气分析，使用默认值
    // 由于Scene类没有相关方法，我们使用默认的晴天
    return WeatherType.CLEAR;
  }

  /**
   * 分析时间
   * @param scene 场景
   * @returns 时间 (24小时制)
   */
  private analyzeTimeOfDay(scene: Scene): number {
    // 简化的时间分析，使用默认值
    // 由于Scene类没有相关方法，我们使用默认的中午12点
    return 12;
  }

  /**
   * 分析环境类型
   * @param scene 场景
   * @returns 环境类型
   */
  private analyzeEnvironmentType(scene: Scene): EnvironmentType {
    // 简化的环境类型分析，使用默认值
    // 由于Scene类没有getTags方法，我们使用默认的户外环境
    return EnvironmentType.OUTDOOR;
  }

  /**
   * 分析地形类型
   * @param scene 场景
   * @returns 地形类型
   */
  private analyzeTerrainType(scene: Scene): TerrainType {
    // 简化的地形类型分析，使用默认值
    // 由于Scene类没有相关方法，我们使用默认的平地地形
    return TerrainType.FLAT;
  }

  /**
   * 计算温度
   * @param environmentType 环境类型
   * @param timeOfDay 时间
   * @param weatherType 天气类型
   * @returns 温度 (摄氏度)
   */
  private calculateTemperature(environmentType: EnvironmentType, timeOfDay: number, weatherType: WeatherType): number {
    let baseTemp = 20; // 默认温度

    // 根据环境类型调整基础温度
    switch (environmentType) {
      case EnvironmentType.DESERT:
        baseTemp = 35;
        break;
      case EnvironmentType.SNOW:
        baseTemp = -5;
        break;
      case EnvironmentType.CAVE:
        baseTemp = 10;
        break;
      case EnvironmentType.UNDERWATER:
        baseTemp = 15;
        break;
      case EnvironmentType.SPACE:
        baseTemp = -270;
        break;
      case EnvironmentType.FOREST:
        baseTemp = 22;
        break;
      case EnvironmentType.INDOOR:
        baseTemp = 22;
        break;
    }

    // 根据时间调整温度
    const timeAdjustment = Math.sin((timeOfDay - 12) * Math.PI / 12) * 5;

    // 根据天气调整温度
    let weatherAdjustment = 0;
    switch (weatherType) {
      case WeatherType.SNOWY:
        weatherAdjustment = -10;
        break;
      case WeatherType.RAINY:
        weatherAdjustment = -5;
        break;
      case WeatherType.CLOUDY:
        weatherAdjustment = -2;
        break;
      case WeatherType.STORMY:
        weatherAdjustment = -7;
        break;
    }

    return baseTemp + timeAdjustment + weatherAdjustment;
  }

  /**
   * 计算湿度
   * @param weatherType 天气类型
   * @param environmentType 环境类型
   * @returns 湿度 (0-1)
   */
  private calculateHumidity(weatherType: WeatherType, environmentType: EnvironmentType): number {
    let baseHumidity = 0.5; // 默认湿度

    // 根据天气调整湿度
    switch (weatherType) {
      case WeatherType.RAINY:
        baseHumidity = 0.9;
        break;
      case WeatherType.SNOWY:
        baseHumidity = 0.7;
        break;
      case WeatherType.FOGGY:
        baseHumidity = 0.8;
        break;
      case WeatherType.STORMY:
        baseHumidity = 0.85;
        break;
      case WeatherType.CLEAR:
        baseHumidity = 0.4;
        break;
    }

    // 根据环境类型调整湿度
    switch (environmentType) {
      case EnvironmentType.DESERT:
        baseHumidity *= 0.3;
        break;
      case EnvironmentType.UNDERWATER:
        baseHumidity = 1.0;
        break;
      case EnvironmentType.FOREST:
        baseHumidity *= 1.2;
        break;
      case EnvironmentType.CAVE:
        baseHumidity *= 1.1;
        break;
    }

    // 确保湿度在0-1范围内
    return Math.max(0, Math.min(1, baseHumidity));
  }

  /**
   * 计算可见度
   * @param weatherType 天气类型
   * @param lightIntensity 光照强度
   * @returns 可见度 (米)
   */
  private calculateVisibility(weatherType: WeatherType, lightIntensity: number): number {
    let baseVisibility = 1000; // 默认可见度

    // 根据天气调整可见度
    switch (weatherType) {
      case WeatherType.FOGGY:
        baseVisibility = 50;
        break;
      case WeatherType.RAINY:
        baseVisibility = 200;
        break;
      case WeatherType.SNOWY:
        baseVisibility = 100;
        break;
      case WeatherType.STORMY:
        baseVisibility = 150;
        break;
      case WeatherType.CLOUDY:
        baseVisibility = 500;
        break;
    }

    // 根据光照强度调整可见度
    const lightAdjustment = lightIntensity * 500;

    return baseVisibility + lightAdjustment;
  }

  /**
   * 更新实体的环境感知数据
   * @param entity 实体
   */
  private updateEntityEnvironmentData(entity: Entity): void {
    const awarenessComponent = entity.getComponent('EnvironmentAwarenessComponent');
    if (!awarenessComponent) {
      return;
    }

    // 获取实体位置
    const transform = entity.getComponent('Transform');
    if (!transform) {
      // 如果实体没有变换组件，使用全局环境数据
      (awarenessComponent as any).updateEnvironmentData(this.globalEnvironmentData);
      return;
    }

    // 检查实体是否在特定环境区域内
    const transformPosition = (transform as any).position;
    let environmentData = this.globalEnvironmentData;

    // 确保位置是Vector3类型
    let position: Vector3;
    if (transformPosition instanceof Vector3) {
      position = transformPosition;
    } else if (transformPosition && typeof transformPosition === 'object') {
      // 如果是类似{x, y, z}的对象，转换为Vector3
      position = new Vector3(
        transformPosition.x || 0,
        transformPosition.y || 0,
        transformPosition.z || 0
      );
    } else {
      // 如果无法获取位置，使用默认位置
      position = new Vector3(0, 0, 0);
    }

    // 检查所有环境区域
    for (const [zoneId, zone] of this.environmentZones.entries()) {
      if (zone.containsPoint(position)) {
        // 合并区域环境数据和全局环境数据
        environmentData = {
          ...environmentData,
          ...zone.environmentData
        };
        break;
      }
    }

    // 更新实体的环境感知数据
    (awarenessComponent as any).updateEnvironmentData(environmentData);
  }

  /**
   * 处理环境响应
   */
  private processEnvironmentResponses(): void {
    for (const entity of this.responseEntities) {
      const responseComponent = entity.getComponent('EnvironmentResponseComponent');
      const awarenessComponent = entity.getComponent('EnvironmentAwarenessComponent');

      if (!responseComponent || !awarenessComponent) {
        continue;
      }

      // 获取环境数据并触发响应
      const environmentData = (awarenessComponent as any).getEnvironmentData();
      (responseComponent as any).evaluateAndRespond(environmentData);
    }
  }

  /**
   * 更新环境可视化
   */
  private updateVisualization(): void {
    // 实现环境可视化逻辑
    if (this.config.debug) {
      console.log('更新环境可视化');
    }
  }

  /**
   * 添加环境区域
   * @param id 区域ID
   * @param zone 环境区域
   */
  public addEnvironmentZone(id: string, zone: EnvironmentZone): void {
    this.environmentZones.set(id, zone);

    if (this.config.debug) {
      console.log(`添加环境区域: ${id}`);
    }
  }

  /**
   * 移除环境区域
   * @param id 区域ID
   */
  public removeEnvironmentZone(id: string): void {
    this.environmentZones.delete(id);

    if (this.config.debug) {
      console.log(`移除环境区域: ${id}`);
    }
  }

  /**
   * 获取环境区域
   * @param id 区域ID
   * @returns 环境区域
   */
  public getEnvironmentZone(id: string): EnvironmentZone | undefined {
    return this.environmentZones.get(id);
  }

  /**
   * 获取全局环境数据
   * @returns 环境数据
   */
  public getGlobalEnvironmentData(): EnvironmentAwarenessData {
    return { ...this.globalEnvironmentData };
  }

  /**
   * 设置全局环境数据
   * @param data 环境数据
   */
  public setGlobalEnvironmentData(data: Partial<EnvironmentAwarenessData>): void {
    this.globalEnvironmentData = {
      ...this.globalEnvironmentData,
      ...data
    };

    if (this.config.debug) {
      console.log('设置全局环境数据:', this.globalEnvironmentData);
    }
  }
}

/**
 * 环境区域接口
 */
export interface EnvironmentZone {
  // 区域ID
  id: string;
  // 区域名称
  name: string;
  // 区域环境数据
  environmentData: Partial<EnvironmentAwarenessData>;
  // 检查点是否在区域内
  containsPoint: (point: Vector3) => boolean;
}
