"use strict";
/**
 * 环境预设
 *
 * 提供一系列预定义的环境响应规则，可以快速应用到角色上。
 * 这些预设涵盖了常见的环境交互场景，如雨天、雪天、炎热天气等。
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.getAllEnvironmentPresets = exports.createUnderwaterResponse = exports.createDarkEnvironmentResponse = exports.createHotWeatherResponse = exports.createSnowyWeatherResponse = exports.createRainyWeatherResponse = void 0;
var EnvironmentAwarenessComponent_1 = require("../components/EnvironmentAwarenessComponent");
var EnvironmentResponseComponent_1 = require("../components/EnvironmentResponseComponent");
/**
 * 创建雨天响应规则
 * @returns 响应规则
 */
function createRainyWeatherResponse() {
    // 雨天条件
    var rainyCondition = {
        type: 'weather',
        params: { weatherType: EnvironmentAwarenessComponent_1.WeatherType.RAINY },
        evaluate: function (data) { return data.weatherType === EnvironmentAwarenessComponent_1.WeatherType.RAINY; }
    };
    // 室外条件
    var outdoorCondition = {
        type: 'environment',
        params: { environmentType: EnvironmentAwarenessComponent_1.EnvironmentType.OUTDOOR },
        evaluate: function (data) { return data.environmentType === EnvironmentAwarenessComponent_1.EnvironmentType.OUTDOOR; }
    };
    // 播放雨天动画动作
    var playRainyAnimation = {
        type: EnvironmentResponseComponent_1.ResponseType.ANIMATION,
        params: { animationName: 'character_rainy_idle', blendTime: 0.5 },
        execute: function (entity) {
            var animator = entity.getAnimator();
            if (animator) {
                animator.playAnimation('character_rainy_idle', { blendTime: 0.5, loop: true });
            }
        },
        stop: function (entity) {
            var animator = entity.getAnimator();
            if (animator) {
                animator.stopAnimation('character_rainy_idle', { blendTime: 0.5 });
            }
        }
    };
    // 播放雨天音效动作
    var playRainySound = {
        type: EnvironmentResponseComponent_1.ResponseType.SOUND,
        params: { soundName: 'character_rain_reaction', volume: 0.8 },
        execute: function (entity) {
            var audioSource = entity.getAudioSource();
            if (audioSource) {
                audioSource.play('character_rain_reaction', { volume: 0.8, loop: false });
            }
        },
        stop: function (entity) {
            var audioSource = entity.getAudioSource();
            if (audioSource) {
                audioSource.stop('character_rain_reaction');
            }
        }
    };
    // 创建雨天响应规则
    return {
        id: 'rainy_weather_response',
        name: '雨天响应',
        description: '角色在雨天的响应行为',
        responseType: EnvironmentResponseComponent_1.ResponseType.ANIMATION,
        priority: EnvironmentResponseComponent_1.ResponsePriority.MEDIUM,
        conditions: [rainyCondition, outdoorCondition],
        actions: [playRainyAnimation, playRainySound],
        cooldown: 10000,
        enabled: true
    };
}
exports.createRainyWeatherResponse = createRainyWeatherResponse;
/**
 * 创建雪天响应规则
 * @returns 响应规则
 */
function createSnowyWeatherResponse() {
    // 雪天条件
    var snowyCondition = {
        type: 'weather',
        params: { weatherType: EnvironmentAwarenessComponent_1.WeatherType.SNOWY },
        evaluate: function (data) { return data.weatherType === EnvironmentAwarenessComponent_1.WeatherType.SNOWY; }
    };
    // 室外条件
    var outdoorCondition = {
        type: 'environment',
        params: { environmentType: EnvironmentAwarenessComponent_1.EnvironmentType.OUTDOOR },
        evaluate: function (data) { return data.environmentType === EnvironmentAwarenessComponent_1.EnvironmentType.OUTDOOR; }
    };
    // 低温条件
    var coldCondition = {
        type: 'temperature',
        params: { maxTemperature: 0 },
        evaluate: function (data) { return data.temperature <= 0; }
    };
    // 播放雪天动画动作
    var playSnowyAnimation = {
        type: EnvironmentResponseComponent_1.ResponseType.ANIMATION,
        params: { animationName: 'character_cold_idle', blendTime: 0.5 },
        execute: function (entity) {
            var animator = entity.getAnimator();
            if (animator) {
                animator.playAnimation('character_cold_idle', { blendTime: 0.5, loop: true });
            }
        },
        stop: function (entity) {
            var animator = entity.getAnimator();
            if (animator) {
                animator.stopAnimation('character_cold_idle', { blendTime: 0.5 });
            }
        }
    };
    // 播放雪天音效动作
    var playSnowySound = {
        type: EnvironmentResponseComponent_1.ResponseType.SOUND,
        params: { soundName: 'character_cold_reaction', volume: 0.8 },
        execute: function (entity) {
            var audioSource = entity.getAudioSource();
            if (audioSource) {
                audioSource.play('character_cold_reaction', { volume: 0.8, loop: false });
            }
        },
        stop: function (entity) {
            var audioSource = entity.getAudioSource();
            if (audioSource) {
                audioSource.stop('character_cold_reaction');
            }
        }
    };
    // 创建雪天响应规则
    return {
        id: 'snowy_weather_response',
        name: '雪天响应',
        description: '角色在雪天的响应行为',
        responseType: EnvironmentResponseComponent_1.ResponseType.ANIMATION,
        priority: EnvironmentResponseComponent_1.ResponsePriority.MEDIUM,
        conditions: [snowyCondition, outdoorCondition, coldCondition],
        actions: [playSnowyAnimation, playSnowySound],
        cooldown: 15000,
        enabled: true
    };
}
exports.createSnowyWeatherResponse = createSnowyWeatherResponse;
/**
 * 创建炎热天气响应规则
 * @returns 响应规则
 */
function createHotWeatherResponse() {
    // 高温条件
    var hotCondition = {
        type: 'temperature',
        params: { minTemperature: 30 },
        evaluate: function (data) { return data.temperature >= 30; }
    };
    // 室外条件
    var outdoorCondition = {
        type: 'environment',
        params: { environmentType: EnvironmentAwarenessComponent_1.EnvironmentType.OUTDOOR },
        evaluate: function (data) { return data.environmentType === EnvironmentAwarenessComponent_1.EnvironmentType.OUTDOOR; }
    };
    // 晴天条件
    var clearWeatherCondition = {
        type: 'weather',
        params: { weatherType: EnvironmentAwarenessComponent_1.WeatherType.CLEAR },
        evaluate: function (data) { return data.weatherType === EnvironmentAwarenessComponent_1.WeatherType.CLEAR; }
    };
    // 播放炎热天气动画动作
    var playHotAnimation = {
        type: EnvironmentResponseComponent_1.ResponseType.ANIMATION,
        params: { animationName: 'character_hot_idle', blendTime: 0.5 },
        execute: function (entity) {
            var animator = entity.getAnimator();
            if (animator) {
                animator.playAnimation('character_hot_idle', { blendTime: 0.5, loop: true });
            }
        },
        stop: function (entity) {
            var animator = entity.getAnimator();
            if (animator) {
                animator.stopAnimation('character_hot_idle', { blendTime: 0.5 });
            }
        }
    };
    // 播放炎热天气音效动作
    var playHotSound = {
        type: EnvironmentResponseComponent_1.ResponseType.SOUND,
        params: { soundName: 'character_hot_reaction', volume: 0.8 },
        execute: function (entity) {
            var audioSource = entity.getAudioSource();
            if (audioSource) {
                audioSource.play('character_hot_reaction', { volume: 0.8, loop: false });
            }
        },
        stop: function (entity) {
            var audioSource = entity.getAudioSource();
            if (audioSource) {
                audioSource.stop('character_hot_reaction');
            }
        }
    };
    // 创建炎热天气响应规则
    return {
        id: 'hot_weather_response',
        name: '炎热天气响应',
        description: '角色在炎热天气的响应行为',
        responseType: EnvironmentResponseComponent_1.ResponseType.ANIMATION,
        priority: EnvironmentResponseComponent_1.ResponsePriority.MEDIUM,
        conditions: [hotCondition, outdoorCondition, clearWeatherCondition],
        actions: [playHotAnimation, playHotSound],
        cooldown: 20000,
        enabled: true
    };
}
exports.createHotWeatherResponse = createHotWeatherResponse;
/**
 * 创建黑暗环境响应规则
 * @returns 响应规则
 */
function createDarkEnvironmentResponse() {
    // 低光照条件
    var lowLightCondition = {
        type: 'light',
        params: { maxIntensity: 0.3 },
        evaluate: function (data) { return data.lightIntensity <= 0.3; }
    };
    // 夜晚条件
    var nightCondition = {
        type: 'timeOfDay',
        params: { minTime: 19, maxTime: 5 },
        evaluate: function (data) { return data.timeOfDay >= 19 || data.timeOfDay <= 5; }
    };
    // 播放黑暗环境动画动作
    var playDarkAnimation = {
        type: EnvironmentResponseComponent_1.ResponseType.ANIMATION,
        params: { animationName: 'character_dark_idle', blendTime: 0.5 },
        execute: function (entity) {
            var animator = entity.getAnimator();
            if (animator) {
                animator.playAnimation('character_dark_idle', { blendTime: 0.5, loop: true });
            }
        },
        stop: function (entity) {
            var animator = entity.getAnimator();
            if (animator) {
                animator.stopAnimation('character_dark_idle', { blendTime: 0.5 });
            }
        }
    };
    // 播放黑暗环境音效动作
    var playDarkSound = {
        type: EnvironmentResponseComponent_1.ResponseType.SOUND,
        params: { soundName: 'character_dark_reaction', volume: 0.8 },
        execute: function (entity) {
            var audioSource = entity.getAudioSource();
            if (audioSource) {
                audioSource.play('character_dark_reaction', { volume: 0.8, loop: false });
            }
        },
        stop: function (entity) {
            var audioSource = entity.getAudioSource();
            if (audioSource) {
                audioSource.stop('character_dark_reaction');
            }
        }
    };
    // 创建黑暗环境响应规则
    return {
        id: 'dark_environment_response',
        name: '黑暗环境响应',
        description: '角色在黑暗环境的响应行为',
        responseType: EnvironmentResponseComponent_1.ResponseType.ANIMATION,
        priority: EnvironmentResponseComponent_1.ResponsePriority.HIGH,
        conditions: [lowLightCondition, nightCondition],
        actions: [playDarkAnimation, playDarkSound],
        cooldown: 30000,
        enabled: true
    };
}
exports.createDarkEnvironmentResponse = createDarkEnvironmentResponse;
/**
 * 创建水中环境响应规则
 * @returns 响应规则
 */
function createUnderwaterResponse() {
    // 水下环境条件
    var underwaterCondition = {
        type: 'environment',
        params: { environmentType: EnvironmentAwarenessComponent_1.EnvironmentType.UNDERWATER },
        evaluate: function (data) { return data.environmentType === EnvironmentAwarenessComponent_1.EnvironmentType.UNDERWATER; }
    };
    // 水位条件
    var waterLevelCondition = {
        type: 'waterLevel',
        params: { minWaterLevel: 1.0 },
        evaluate: function (data) { return data.waterLevel >= 1.0; }
    };
    // 播放水下动画动作
    var playUnderwaterAnimation = {
        type: EnvironmentResponseComponent_1.ResponseType.ANIMATION,
        params: { animationName: 'character_swim', blendTime: 0.5 },
        execute: function (entity) {
            var animator = entity.getAnimator();
            if (animator) {
                animator.playAnimation('character_swim', { blendTime: 0.5, loop: true });
            }
        },
        stop: function (entity) {
            var animator = entity.getAnimator();
            if (animator) {
                animator.stopAnimation('character_swim', { blendTime: 0.5 });
            }
        }
    };
    // 播放水下音效动作
    var playUnderwaterSound = {
        type: EnvironmentResponseComponent_1.ResponseType.SOUND,
        params: { soundName: 'character_underwater', volume: 0.8 },
        execute: function (entity) {
            var audioSource = entity.getAudioSource();
            if (audioSource) {
                audioSource.play('character_underwater', { volume: 0.8, loop: true });
            }
        },
        stop: function (entity) {
            var audioSource = entity.getAudioSource();
            if (audioSource) {
                audioSource.stop('character_underwater');
            }
        }
    };
    // 创建水下环境响应规则
    return {
        id: 'underwater_response',
        name: '水下环境响应',
        description: '角色在水下环境的响应行为',
        responseType: EnvironmentResponseComponent_1.ResponseType.ANIMATION,
        priority: EnvironmentResponseComponent_1.ResponsePriority.CRITICAL,
        conditions: [underwaterCondition, waterLevelCondition],
        actions: [playUnderwaterAnimation, playUnderwaterSound],
        cooldown: 0,
        enabled: true
    };
}
exports.createUnderwaterResponse = createUnderwaterResponse;
/**
 * 获取所有预设响应规则
 * @returns 响应规则数组
 */
function getAllEnvironmentPresets() {
    return [
        createRainyWeatherResponse(),
        createSnowyWeatherResponse(),
        createHotWeatherResponse(),
        createDarkEnvironmentResponse(),
        createUnderwaterResponse()
    ];
}
exports.getAllEnvironmentPresets = getAllEnvironmentPresets;
