"use strict";
/**
 * 环境感知系统
 *
 * 该系统负责检测和更新场景中的环境信息，并将其传递给具有环境感知组件的实体。
 * 系统会分析场景中的光照、天气、地形等元素，计算环境参数，并触发相应的环境变化事件。
 */
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EnvironmentAwarenessSystem = void 0;
var System_1 = require("../../core/System");
var three_1 = require("three");
var EnvironmentAwarenessComponent_1 = require("../components/EnvironmentAwarenessComponent");
/**
 * 环境感知系统
 */
var EnvironmentAwarenessSystem = /** @class */ (function (_super) {
    __extends(EnvironmentAwarenessSystem, _super);
    /**
     * 构造函数
     * @param world 世界
     * @param config 配置
     */
    function EnvironmentAwarenessSystem(world, config) {
        if (config === void 0) { config = {}; }
        var _this = _super.call(this) || this;
        // 上次更新时间
        _this.lastUpdateTime = 0;
        // 环境感知组件实体列表
        _this.awarenessEntities = [];
        // 环境响应组件实体列表
        _this.responseEntities = [];
        // 环境区域映射
        _this.environmentZones = new Map();
        _this.world = world;
        // 设置默认配置
        _this.config = __assign({ debug: false, updateFrequency: 1000, autoDetect: true, detectionRange: 100, enableResponse: true, usePhysicsRaycasting: false, enableVisualization: false }, config);
        // 初始化射线检测器
        _this.raycaster = new three_1.Raycaster();
        // 初始化全局环境数据
        _this.globalEnvironmentData = _this.createDefaultEnvironmentData();
        if (_this.config.debug) {
            console.log('环境感知系统初始化');
        }
        return _this;
    }
    /**
     * 创建默认环境数据
     * @returns 默认环境数据
     */
    EnvironmentAwarenessSystem.prototype.createDefaultEnvironmentData = function () {
        return {
            environmentType: EnvironmentAwarenessComponent_1.EnvironmentType.OUTDOOR,
            weatherType: EnvironmentAwarenessComponent_1.WeatherType.CLEAR,
            terrainType: EnvironmentAwarenessComponent_1.TerrainType.FLAT,
            lightIntensity: 1.0,
            temperature: 20,
            humidity: 0.5,
            windSpeed: 0,
            windDirection: new three_1.Vector3(1, 0, 0),
            noiseLevel: 0,
            airQuality: 1.0,
            waterLevel: 0,
            visibility: 1000,
            timeOfDay: 12,
            customParameters: new Map(),
            lastEnvironmentChangeTime: Date.now(),
            awarenessRange: this.config.detectionRange || 100
        };
    };
    /**
     * 初始化
     */
    EnvironmentAwarenessSystem.prototype.initialize = function () {
        // 查找所有具有环境感知组件的实体
        this.awarenessEntities = this.findEntitiesWithComponent('EnvironmentAwarenessComponent');
        // 查找所有具有环境响应组件的实体
        this.responseEntities = this.findEntitiesWithComponent('EnvironmentResponseComponent');
        if (this.config.debug) {
            console.log("\u627E\u5230 ".concat(this.awarenessEntities.length, " \u4E2A\u73AF\u5883\u611F\u77E5\u5B9E\u4F53\u548C ").concat(this.responseEntities.length, " \u4E2A\u73AF\u5883\u54CD\u5E94\u5B9E\u4F53"));
        }
    };
    /**
     * 查找具有指定组件的实体
     * @param componentType 组件类型
     * @returns 实体数组
     */
    EnvironmentAwarenessSystem.prototype.findEntitiesWithComponent = function (componentType) {
        var result = [];
        if (!this.world)
            return result;
        var allEntities = this.world.getAllEntities();
        for (var _i = 0, allEntities_1 = allEntities; _i < allEntities_1.length; _i++) {
            var entity = allEntities_1[_i];
            if (entity.hasComponent(componentType)) {
                result.push(entity);
            }
        }
        return result;
    };
    /**
     * 更新
     * @param deltaTime 时间增量
     */
    EnvironmentAwarenessSystem.prototype.update = function (deltaTime) {
        var now = Date.now();
        // 检查是否需要更新
        if (now - this.lastUpdateTime < this.config.updateFrequency) {
            return;
        }
        this.lastUpdateTime = now;
        // 更新实体列表
        this.awarenessEntities = this.findEntitiesWithComponent('EnvironmentAwarenessComponent');
        this.responseEntities = this.findEntitiesWithComponent('EnvironmentResponseComponent');
        // 如果启用自动检测，更新全局环境数据
        if (this.config.autoDetect) {
            this.updateGlobalEnvironmentData();
        }
        // 更新每个实体的环境感知数据
        for (var _i = 0, _a = this.awarenessEntities; _i < _a.length; _i++) {
            var entity = _a[_i];
            this.updateEntityEnvironmentData(entity);
        }
        // 如果启用环境响应，处理环境响应
        if (this.config.enableResponse) {
            this.processEnvironmentResponses();
        }
        // 如果启用环境可视化，更新可视化
        if (this.config.enableVisualization) {
            this.updateVisualization();
        }
    };
    /**
     * 更新全局环境数据
     */
    EnvironmentAwarenessSystem.prototype.updateGlobalEnvironmentData = function () {
        // 获取当前场景
        var scene = this.world.getActiveScene();
        if (!scene) {
            return;
        }
        // 分析场景环境
        var environmentData = this.analyzeSceneEnvironment(scene);
        // 更新全局环境数据
        this.globalEnvironmentData = __assign(__assign({}, this.globalEnvironmentData), environmentData);
        if (this.config.debug) {
            console.log('全局环境数据已更新:', this.globalEnvironmentData);
        }
    };
    /**
     * 分析场景环境
     * @param scene 场景
     * @returns 环境数据
     */
    EnvironmentAwarenessSystem.prototype.analyzeSceneEnvironment = function (scene) {
        var data = {};
        // 分析光照
        data.lightIntensity = this.analyzeLighting(scene);
        // 分析天气
        data.weatherType = this.analyzeWeather(scene);
        // 分析时间
        data.timeOfDay = this.analyzeTimeOfDay(scene);
        // 分析环境类型
        data.environmentType = this.analyzeEnvironmentType(scene);
        // 分析地形
        data.terrainType = this.analyzeTerrainType(scene);
        // 分析其他环境参数
        data.temperature = this.calculateTemperature(data.environmentType, data.timeOfDay, data.weatherType);
        data.humidity = this.calculateHumidity(data.weatherType, data.environmentType);
        data.visibility = this.calculateVisibility(data.weatherType, data.lightIntensity);
        return data;
    };
    /**
     * 分析光照
     * @param scene 场景
     * @returns 光照强度
     */
    EnvironmentAwarenessSystem.prototype.analyzeLighting = function (scene) {
        // 简化的光照分析，使用默认值
        // 由于Scene类没有getLights方法，我们使用默认的中等光照
        return 0.5;
    };
    /**
     * 分析天气
     * @param scene 场景
     * @returns 天气类型
     */
    EnvironmentAwarenessSystem.prototype.analyzeWeather = function (scene) {
        // 简化的天气分析，使用默认值
        // 由于Scene类没有相关方法，我们使用默认的晴天
        return EnvironmentAwarenessComponent_1.WeatherType.CLEAR;
    };
    /**
     * 分析时间
     * @param scene 场景
     * @returns 时间 (24小时制)
     */
    EnvironmentAwarenessSystem.prototype.analyzeTimeOfDay = function (scene) {
        // 简化的时间分析，使用默认值
        // 由于Scene类没有相关方法，我们使用默认的中午12点
        return 12;
    };
    /**
     * 分析环境类型
     * @param scene 场景
     * @returns 环境类型
     */
    EnvironmentAwarenessSystem.prototype.analyzeEnvironmentType = function (scene) {
        // 简化的环境类型分析，使用默认值
        // 由于Scene类没有getTags方法，我们使用默认的户外环境
        return EnvironmentAwarenessComponent_1.EnvironmentType.OUTDOOR;
    };
    /**
     * 分析地形类型
     * @param scene 场景
     * @returns 地形类型
     */
    EnvironmentAwarenessSystem.prototype.analyzeTerrainType = function (scene) {
        // 简化的地形类型分析，使用默认值
        // 由于Scene类没有相关方法，我们使用默认的平地地形
        return EnvironmentAwarenessComponent_1.TerrainType.FLAT;
    };
    /**
     * 计算温度
     * @param environmentType 环境类型
     * @param timeOfDay 时间
     * @param weatherType 天气类型
     * @returns 温度 (摄氏度)
     */
    EnvironmentAwarenessSystem.prototype.calculateTemperature = function (environmentType, timeOfDay, weatherType) {
        var baseTemp = 20; // 默认温度
        // 根据环境类型调整基础温度
        switch (environmentType) {
            case EnvironmentAwarenessComponent_1.EnvironmentType.DESERT:
                baseTemp = 35;
                break;
            case EnvironmentAwarenessComponent_1.EnvironmentType.SNOW:
                baseTemp = -5;
                break;
            case EnvironmentAwarenessComponent_1.EnvironmentType.CAVE:
                baseTemp = 10;
                break;
            case EnvironmentAwarenessComponent_1.EnvironmentType.UNDERWATER:
                baseTemp = 15;
                break;
            case EnvironmentAwarenessComponent_1.EnvironmentType.SPACE:
                baseTemp = -270;
                break;
            case EnvironmentAwarenessComponent_1.EnvironmentType.FOREST:
                baseTemp = 22;
                break;
            case EnvironmentAwarenessComponent_1.EnvironmentType.INDOOR:
                baseTemp = 22;
                break;
        }
        // 根据时间调整温度
        var timeAdjustment = Math.sin((timeOfDay - 12) * Math.PI / 12) * 5;
        // 根据天气调整温度
        var weatherAdjustment = 0;
        switch (weatherType) {
            case EnvironmentAwarenessComponent_1.WeatherType.SNOWY:
                weatherAdjustment = -10;
                break;
            case EnvironmentAwarenessComponent_1.WeatherType.RAINY:
                weatherAdjustment = -5;
                break;
            case EnvironmentAwarenessComponent_1.WeatherType.CLOUDY:
                weatherAdjustment = -2;
                break;
            case EnvironmentAwarenessComponent_1.WeatherType.STORMY:
                weatherAdjustment = -7;
                break;
        }
        return baseTemp + timeAdjustment + weatherAdjustment;
    };
    /**
     * 计算湿度
     * @param weatherType 天气类型
     * @param environmentType 环境类型
     * @returns 湿度 (0-1)
     */
    EnvironmentAwarenessSystem.prototype.calculateHumidity = function (weatherType, environmentType) {
        var baseHumidity = 0.5; // 默认湿度
        // 根据天气调整湿度
        switch (weatherType) {
            case EnvironmentAwarenessComponent_1.WeatherType.RAINY:
                baseHumidity = 0.9;
                break;
            case EnvironmentAwarenessComponent_1.WeatherType.SNOWY:
                baseHumidity = 0.7;
                break;
            case EnvironmentAwarenessComponent_1.WeatherType.FOGGY:
                baseHumidity = 0.8;
                break;
            case EnvironmentAwarenessComponent_1.WeatherType.STORMY:
                baseHumidity = 0.85;
                break;
            case EnvironmentAwarenessComponent_1.WeatherType.CLEAR:
                baseHumidity = 0.4;
                break;
        }
        // 根据环境类型调整湿度
        switch (environmentType) {
            case EnvironmentAwarenessComponent_1.EnvironmentType.DESERT:
                baseHumidity *= 0.3;
                break;
            case EnvironmentAwarenessComponent_1.EnvironmentType.UNDERWATER:
                baseHumidity = 1.0;
                break;
            case EnvironmentAwarenessComponent_1.EnvironmentType.FOREST:
                baseHumidity *= 1.2;
                break;
            case EnvironmentAwarenessComponent_1.EnvironmentType.CAVE:
                baseHumidity *= 1.1;
                break;
        }
        // 确保湿度在0-1范围内
        return Math.max(0, Math.min(1, baseHumidity));
    };
    /**
     * 计算可见度
     * @param weatherType 天气类型
     * @param lightIntensity 光照强度
     * @returns 可见度 (米)
     */
    EnvironmentAwarenessSystem.prototype.calculateVisibility = function (weatherType, lightIntensity) {
        var baseVisibility = 1000; // 默认可见度
        // 根据天气调整可见度
        switch (weatherType) {
            case EnvironmentAwarenessComponent_1.WeatherType.FOGGY:
                baseVisibility = 50;
                break;
            case EnvironmentAwarenessComponent_1.WeatherType.RAINY:
                baseVisibility = 200;
                break;
            case EnvironmentAwarenessComponent_1.WeatherType.SNOWY:
                baseVisibility = 100;
                break;
            case EnvironmentAwarenessComponent_1.WeatherType.STORMY:
                baseVisibility = 150;
                break;
            case EnvironmentAwarenessComponent_1.WeatherType.CLOUDY:
                baseVisibility = 500;
                break;
        }
        // 根据光照强度调整可见度
        var lightAdjustment = lightIntensity * 500;
        return baseVisibility + lightAdjustment;
    };
    /**
     * 更新实体的环境感知数据
     * @param entity 实体
     */
    EnvironmentAwarenessSystem.prototype.updateEntityEnvironmentData = function (entity) {
        var awarenessComponent = entity.getComponent('EnvironmentAwarenessComponent');
        if (!awarenessComponent) {
            return;
        }
        // 获取实体位置
        var transform = entity.getComponent('Transform');
        if (!transform) {
            // 如果实体没有变换组件，使用全局环境数据
            awarenessComponent.updateEnvironmentData(this.globalEnvironmentData);
            return;
        }
        // 检查实体是否在特定环境区域内
        var transformPosition = transform.position;
        var environmentData = this.globalEnvironmentData;
        // 确保位置是Vector3类型
        var position;
        if (transformPosition instanceof three_1.Vector3) {
            position = transformPosition;
        }
        else if (transformPosition && typeof transformPosition === 'object') {
            // 如果是类似{x, y, z}的对象，转换为Vector3
            position = new three_1.Vector3(transformPosition.x || 0, transformPosition.y || 0, transformPosition.z || 0);
        }
        else {
            // 如果无法获取位置，使用默认位置
            position = new three_1.Vector3(0, 0, 0);
        }
        // 检查所有环境区域
        for (var _i = 0, _a = this.environmentZones.entries(); _i < _a.length; _i++) {
            var _b = _a[_i], zone = _b[1];
            if (zone.containsPoint(position)) {
                // 合并区域环境数据和全局环境数据
                environmentData = __assign(__assign({}, environmentData), zone.environmentData);
                break;
            }
        }
        // 更新实体的环境感知数据
        awarenessComponent.updateEnvironmentData(environmentData);
    };
    /**
     * 处理环境响应
     */
    EnvironmentAwarenessSystem.prototype.processEnvironmentResponses = function () {
        for (var _i = 0, _a = this.responseEntities; _i < _a.length; _i++) {
            var entity = _a[_i];
            var responseComponent = entity.getComponent('EnvironmentResponseComponent');
            var awarenessComponent = entity.getComponent('EnvironmentAwarenessComponent');
            if (!responseComponent || !awarenessComponent) {
                continue;
            }
            // 获取环境数据并触发响应
            var environmentData = awarenessComponent.getEnvironmentData();
            responseComponent.evaluateAndRespond(environmentData);
        }
    };
    /**
     * 更新环境可视化
     */
    EnvironmentAwarenessSystem.prototype.updateVisualization = function () {
        // 实现环境可视化逻辑
        if (this.config.debug) {
            console.log('更新环境可视化');
        }
    };
    /**
     * 添加环境区域
     * @param id 区域ID
     * @param zone 环境区域
     */
    EnvironmentAwarenessSystem.prototype.addEnvironmentZone = function (id, zone) {
        this.environmentZones.set(id, zone);
        if (this.config.debug) {
            console.log("\u6DFB\u52A0\u73AF\u5883\u533A\u57DF: ".concat(id));
        }
    };
    /**
     * 移除环境区域
     * @param id 区域ID
     */
    EnvironmentAwarenessSystem.prototype.removeEnvironmentZone = function (id) {
        this.environmentZones.delete(id);
        if (this.config.debug) {
            console.log("\u79FB\u9664\u73AF\u5883\u533A\u57DF: ".concat(id));
        }
    };
    /**
     * 获取环境区域
     * @param id 区域ID
     * @returns 环境区域
     */
    EnvironmentAwarenessSystem.prototype.getEnvironmentZone = function (id) {
        return this.environmentZones.get(id);
    };
    /**
     * 获取全局环境数据
     * @returns 环境数据
     */
    EnvironmentAwarenessSystem.prototype.getGlobalEnvironmentData = function () {
        return __assign({}, this.globalEnvironmentData);
    };
    /**
     * 设置全局环境数据
     * @param data 环境数据
     */
    EnvironmentAwarenessSystem.prototype.setGlobalEnvironmentData = function (data) {
        this.globalEnvironmentData = __assign(__assign({}, this.globalEnvironmentData), data);
        if (this.config.debug) {
            console.log('设置全局环境数据:', this.globalEnvironmentData);
        }
    };
    return EnvironmentAwarenessSystem;
}(System_1.System));
exports.EnvironmentAwarenessSystem = EnvironmentAwarenessSystem;
